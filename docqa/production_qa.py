#!/usr/bin/env python3
"""
Production-Grade Question Answering System
Using best practices for RAG with proper prompt engineering
"""

import asyncio
import sys
import json
import time
from typing import Dict, List, Any, Optional

# Add the project root to the path
sys.path.append('.')

from .vector_store.production_embeddings import ProductionEmbeddingService
from .vector_store.production_vector_store import ProductionVectorStore, RetrievalResult

class ProductionQASystem:
    """Production-grade QA system with structured prompts and reranking"""
    
    def __init__(self):
        self.embedding_service = ProductionEmbeddingService()
        self.vector_store = ProductionVectorStore()
        
        # Initialize OpenAI client for answer generation
        from docqa.config import get_config
        import openai
        
        self.config = get_config()
        self.openai_client = openai.OpenAI(api_key=self.config.openai_api_key)
        self.chat_model = self.config.chat_model
        self.timeout_seconds = 60  # Configurable timeout for Q&A operations
        
        print(f"✅ Production QA System initialized")
        print(f"   Embedding Model: {self.embedding_service.model_name}")
        print(f"   Chat Model: {self.chat_model}")
        print(f"   Timeout: {self.timeout_seconds} seconds")
    
    async def answer_question(
        self,
        question: str,
        franchisor_id: Optional[str] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        temperature: float = 0.1,
        max_tokens: int = 800
    ) -> Dict[str, Any]:
        """
        Answer question using production-grade RAG
        
        Args:
            question: User question
            franchisor_id: Optional franchisor filter
            top_k: Number of chunks to retrieve (3-10 recommended)
            similarity_threshold: Minimum similarity (0.7-0.8 recommended)
            temperature: Generation temperature (0.0-0.3 for factual)
            max_tokens: Maximum response tokens
            
        Returns:
            Dict with answer, sources, and metadata
        """
        start_time = time.time()
        
        try:
            print(f"🔍 Processing question: {question}")
            
            # Step 1: Generate query embedding with timeout
            try:
                query_embedding = await asyncio.wait_for(
                    asyncio.to_thread(self.embedding_service.generate_embedding, question),
                    timeout=self.timeout_seconds
                )
                print(f"✅ Generated query embedding: {len(query_embedding)} dimensions")
            except asyncio.TimeoutError:
                return {
                    'success': False,
                    'error': f'Embedding generation timed out after {self.timeout_seconds} seconds',
                    'answer': "I'm sorry, the system is taking too long to process your question. Please try again.",
                    'sources': [],
                    'metadata': {
                        'processing_time': time.time() - start_time,
                        'error_type': 'timeout'
                    }
                }
            
            # Step 2: Retrieve relevant chunks with multiple thresholds and timeout
            try:
                retrieved_chunks = await asyncio.wait_for(
                    self._retrieve_with_fallback(
                        query_embedding=query_embedding,
                        franchisor_id=franchisor_id,
                        top_k=top_k,
                        similarity_threshold=similarity_threshold
                    ),
                    timeout=self.timeout_seconds
                )
            except asyncio.TimeoutError:
                return {
                    'success': False,
                    'error': f'Document search timed out after {self.timeout_seconds} seconds',
                    'answer': "I'm sorry, the search is taking too long. Please try rephrasing your question.",
                    'sources': [],
                    'metadata': {
                        'processing_time': time.time() - start_time,
                        'error_type': 'timeout'
                    }
                }
            
            if not retrieved_chunks:
                return {
                    'success': True,
                    'answer': "Answer not found in document.",
                    'sources': [],
                    'metadata': {
                        'processing_time': time.time() - start_time,
                        'chunks_found': 0,
                        'similarity_threshold_used': similarity_threshold
                    }
                }
            
            print(f"✅ Retrieved {len(retrieved_chunks)} chunks")
            
            # Step 3: Rerank chunks (simple scoring for now)
            reranked_chunks = self._rerank_chunks(retrieved_chunks, question)
            print(f"✅ Reranked chunks, top score: {reranked_chunks[0].similarity_score:.4f}")
            
            # Step 4: Generate answer with structured prompt and timeout
            try:
                answer = await asyncio.wait_for(
                    self._generate_answer(
                        question=question,
                        chunks=reranked_chunks,
                        temperature=temperature,
                        max_tokens=max_tokens
                    ),
                    timeout=self.timeout_seconds
                )
            except asyncio.TimeoutError:
                return {
                    'success': False,
                    'error': f'Answer generation timed out after {self.timeout_seconds} seconds',
                    'answer': "I'm sorry, generating the answer is taking too long. Please try a simpler question.",
                    'sources': [],
                    'metadata': {
                        'processing_time': time.time() - start_time,
                        'error_type': 'timeout'
                    }
                }
            
            # Step 5: Format response
            response = {
                'success': True,
                'answer': answer,
                'sources': [
                    {
                        'text': chunk.text,
                        'similarity_score': chunk.similarity_score,
                        'metadata': chunk.metadata,
                        'source_info': chunk.source_info
                    }
                    for chunk in reranked_chunks
                ],
                'metadata': {
                    'processing_time': time.time() - start_time,
                    'chunks_found': len(retrieved_chunks),
                    'chunks_used': len(reranked_chunks),
                    'similarity_threshold_used': similarity_threshold,
                    'model_used': self.chat_model,
                    'temperature': temperature
                }
            }
            
            print(f"✅ Generated answer: {len(answer)} characters")
            return response
            
        except Exception as e:
            print(f"❌ Error in QA system: {e}")
            import traceback
            traceback.print_exc()
            
            return {
                'success': False,
                'error': str(e),
                'answer': "An error occurred while processing your question.",
                'sources': [],
                'metadata': {
                    'processing_time': time.time() - start_time,
                    'error_type': type(e).__name__
                }
            }
    
    async def _retrieve_with_fallback(
        self,
        query_embedding: List[float],
        franchisor_id: Optional[str],
        top_k: int,
        similarity_threshold: float
    ) -> List[RetrievalResult]:
        """Retrieve chunks with fallback to lower thresholds"""
        
        # Try different thresholds in descending order
        thresholds = [similarity_threshold, 0.5, 0.3, 0.1]
        
        for threshold in thresholds:
            print(f"  Trying similarity threshold: {threshold}")
            
            results = self.vector_store.search_similar(
                query_embedding=query_embedding,
                top_k=top_k,
                similarity_threshold=threshold,
                franchisor_id=franchisor_id
            )
            
            if results:
                print(f"  ✅ Found {len(results)} results with threshold {threshold}")
                return results
        
        print(f"  ❌ No results found with any threshold")
        return []
    
    def _rerank_chunks(self, chunks: List[RetrievalResult], question: str) -> List[RetrievalResult]:
        """Simple reranking based on similarity score and text length"""
        
        # For now, just sort by similarity score
        # In production, you might use BGE-Reranker, Cohere-Rerank, or ColBERT
        reranked = sorted(chunks, key=lambda x: x.similarity_score, reverse=True)
        
        return reranked
    
    async def _generate_answer(
        self,
        question: str,
        chunks: List[RetrievalResult],
        temperature: float,
        max_tokens: int
    ) -> str:
        """Generate answer using structured prompt"""
        
        # Prepare context from chunks
        context_parts = []
        for i, chunk in enumerate(chunks, 1):
            context_parts.append(f"Source {i} (Similarity: {chunk.similarity_score:.3f}):\n{chunk.text}")
        
        context = "\n\n".join(context_parts)
        
        # Structured prompt following best practices
        system_prompt = """You are an assistant answering based on context only.

INSTRUCTIONS:
1. Answer based ONLY on the provided context
2. If you don't know or the context doesn't contain the information, say "Answer not found in document."
3. Be specific and cite relevant details from the context
4. Keep answers concise but informative
5. Do not make up information not in the context

CONTEXT FORMAT:
Each source shows similarity score and content. Higher similarity scores indicate more relevant content."""

        user_prompt = f"""Context:
{context}

Question: {question}

Answer:"""
        
        try:
            # Generate answer with OpenAI
            response = self.openai_client.chat.completions.create(
                model=self.chat_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens,
                stream=False
            )
            
            answer = response.choices[0].message.content.strip()
            
            # Log token usage
            if response.usage:
                print(f"  Token usage: {response.usage.total_tokens} total "
                      f"({response.usage.prompt_tokens} prompt + {response.usage.completion_tokens} completion)")
            
            return answer
            
        except Exception as e:
            print(f"❌ Error generating answer: {e}")
            return "An error occurred while generating the answer."

async def test_production_qa():
    """Test the production QA system"""
    print("🚀 Testing Production QA System")
    print("=" * 50)
    
    # Initialize QA system
    qa_system = ProductionQASystem()
    
    # Test questions
    test_questions = [
        "What is Coochie Hydrogreen?",
        "Where is Coochie Hydrogreen located?",
        "What services does Coochie Hydrogreen provide?",
        "What are the franchise fees?",
        "What training does the business provide?",
        "What is the franchisee approval process?",
        "What are the marketing fees?",
        "What equipment is needed?",
        "What territories are available?",
        "What support is provided to franchisees?"
    ]
    
    franchisor_id = "569976f2-d845-4615-8a91-96e18086adbe"
    
    # Test with production-grade parameters
    results = []
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. ❓ Question: {question}")
        
        # Use production-grade parameters
        result = await qa_system.answer_question(
            question=question,
            franchisor_id=franchisor_id,
            top_k=5,                    # Moderate retrieval
            similarity_threshold=0.5,   # Balanced threshold
            temperature=0.1,            # Low temperature for factual answers
            max_tokens=600              # Sufficient for detailed answers
        )
        
        if result['success']:
            answer = result['answer']
            sources_count = len(result['sources'])
            processing_time = result['metadata']['processing_time']
            
            print(f"   ✅ Answer ({processing_time:.2f}s, {sources_count} sources):")
            print(f"   {answer}")
            
            # Show top source
            if result['sources']:
                top_source = result['sources'][0]
                print(f"   📚 Top Source (Score: {top_source['similarity_score']:.3f}):")
                print(f"   {top_source['text'][:100]}...")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        results.append(result)
        
        # Small delay between questions
        await asyncio.sleep(0.5)
    
    # Summary
    successful_answers = sum(1 for r in results if r['success'] and 'Answer not found' not in r['answer'])
    print(f"\n📊 Summary: {successful_answers}/{len(test_questions)} questions answered successfully")
    
    return results

async def main():
    """Main function"""
    results = await test_production_qa()
    
    # Save results
    with open('production_qa_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print("\n✅ Results saved to production_qa_results.json")

if __name__ == "__main__":
    asyncio.run(main())
