"""add_lead_reference_tables

Revision ID: 006_add_lead_reference_tables
Revises: 002_add_holiday_and_messaging_rule_tables, 003pgvector, 004_add_document_processing_status, 005_add_conversational_chatbot_tables, 46c979418266
Create Date: 2025-07-23 19:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '006_lead_refs'
down_revision = ('002_add_holiday_and_messaging_rule_tables', '003pgvector', '004_doc_status', '005_add_conversational_chatbot_tables', '46c979418266')
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add lead reference tables"""
    
    # Create lead_sources table
    op.create_table(
        'lead_sources',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('name', sa.String(100), nullable=False, unique=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )
    
    # Create indexes for lead_sources
    op.create_index(op.f('ix_lead_sources_id'), 'lead_sources', ['id'], unique=False)
    op.create_index(op.f('ix_lead_sources_name'), 'lead_sources', ['name'], unique=False)
    op.create_index(op.f('ix_lead_sources_is_active'), 'lead_sources', ['is_active'], unique=False)
    op.create_index(op.f('ix_lead_sources_is_deleted'), 'lead_sources', ['is_deleted'], unique=False)
    
    # Create lead_statuses table
    op.create_table(
        'lead_statuses',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('uuid_generate_v4()'), nullable=False),
        sa.Column('name', sa.String(100), nullable=False, unique=True),
        sa.Column('colour', sa.String(7), nullable=False),  # Hex color code
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
    )
    
    # Create indexes for lead_statuses
    op.create_index(op.f('ix_lead_statuses_id'), 'lead_statuses', ['id'], unique=False)
    op.create_index(op.f('ix_lead_statuses_name'), 'lead_statuses', ['name'], unique=False)
    op.create_index(op.f('ix_lead_statuses_is_active'), 'lead_statuses', ['is_active'], unique=False)
    op.create_index(op.f('ix_lead_statuses_is_deleted'), 'lead_statuses', ['is_deleted'], unique=False)
    
    # Update leads table to add foreign key columns if they don't exist
    # Check if columns exist first
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    leads_columns = [col['name'] for col in inspector.get_columns('leads')]
    
    if 'lead_source_id' not in leads_columns:
        op.add_column('leads', sa.Column('lead_source_id', postgresql.UUID(as_uuid=True), nullable=True))
        op.create_index(op.f('ix_leads_lead_source_id'), 'leads', ['lead_source_id'], unique=False)
        op.create_foreign_key('fk_leads_lead_source_id', 'leads', 'lead_sources', ['lead_source_id'], ['id'])
    
    if 'lead_status_id' not in leads_columns:
        op.add_column('leads', sa.Column('lead_status_id', postgresql.UUID(as_uuid=True), nullable=True))
        op.create_index(op.f('ix_leads_lead_status_id'), 'leads', ['lead_status_id'], unique=False)
        op.create_foreign_key('fk_leads_lead_status_id', 'leads', 'lead_statuses', ['lead_status_id'], ['id'])
    
    # Insert default lead sources
    op.execute("""
        INSERT INTO lead_sources (name, is_active, is_deleted) VALUES
        ('Website', true, false),
        ('Phone Call', true, false),
        ('Email', true, false),
        ('Referral', true, false),
        ('Social Media', true, false),
        ('Trade Show', true, false),
        ('Advertisement', true, false),
        ('Other', true, false)
        ON CONFLICT (name) DO NOTHING;
    """)
    
    # Insert default lead statuses
    op.execute("""
        INSERT INTO lead_statuses (name, colour, is_active, is_deleted) VALUES
        ('New', '#007bff', true, false),
        ('Contacted', '#ffc107', true, false),
        ('Qualified', '#28a745', true, false),
        ('Unqualified', '#dc3545', true, false),
        ('Converted', '#17a2b8', true, false),
        ('Lost', '#6c757d', true, false)
        ON CONFLICT (name) DO NOTHING;
    """)


def downgrade() -> None:
    """Remove lead reference tables"""
    
    # Remove foreign key constraints first
    op.drop_constraint('fk_leads_lead_status_id', 'leads', type_='foreignkey')
    op.drop_constraint('fk_leads_lead_source_id', 'leads', type_='foreignkey')
    
    # Remove columns from leads table
    op.drop_index(op.f('ix_leads_lead_status_id'), table_name='leads')
    op.drop_column('leads', 'lead_status_id')
    
    op.drop_index(op.f('ix_leads_lead_source_id'), table_name='leads')
    op.drop_column('leads', 'lead_source_id')
    
    # Drop lead_statuses table
    op.drop_index(op.f('ix_lead_statuses_is_deleted'), table_name='lead_statuses')
    op.drop_index(op.f('ix_lead_statuses_is_active'), table_name='lead_statuses')
    op.drop_index(op.f('ix_lead_statuses_name'), table_name='lead_statuses')
    op.drop_index(op.f('ix_lead_statuses_id'), table_name='lead_statuses')
    op.drop_table('lead_statuses')
    
    # Drop lead_sources table
    op.drop_index(op.f('ix_lead_sources_is_deleted'), table_name='lead_sources')
    op.drop_index(op.f('ix_lead_sources_is_active'), table_name='lead_sources')
    op.drop_index(op.f('ix_lead_sources_name'), table_name='lead_sources')
    op.drop_index(op.f('ix_lead_sources_id'), table_name='lead_sources')
    op.drop_table('lead_sources')
