"""create_franchisor_chunks_table

Revision ID: 008_create_franchisor_chunks
Revises: 007_lead_refs_simple
Create Date: 2025-07-24 11:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from pgvector.sqlalchemy import Vector

# revision identifiers, used by Alembic.
revision = '008_create_franchisor_chunks'
down_revision = '4f6afec85cf6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create franchisor_chunks table for storing document chunks with embeddings"""
    
    # Check if table already exists
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    existing_tables = inspector.get_table_names()
    
    if 'franchisor_chunks' not in existing_tables:
        # Create franchisor_chunks table
        op.create_table(
            'franchisor_chunks',
            sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()'), nullable=False),
            sa.Column('franchisor_id', postgresql.UUID(as_uuid=True), nullable=False),
            sa.Column('text', sa.Text(), nullable=False),
            sa.Column('embedding', Vector(1536), nullable=True),
            sa.Column('chunk_index', sa.Integer(), nullable=False),
            sa.Column('token_count', sa.Integer(), nullable=False),
            sa.Column('metadata', postgresql.JSONB(), nullable=False, server_default='{}'),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
        )
        
        # Create foreign key constraint
        op.create_foreign_key(
            'fk_franchisor_chunks_franchisor',
            'franchisor_chunks',
            'franchisors',
            ['franchisor_id'],
            ['id'],
            ondelete='CASCADE'
        )
        
        # Create indexes
        op.create_index(op.f('ix_franchisor_chunks_id'), 'franchisor_chunks', ['id'], unique=False)
        op.create_index(op.f('ix_franchisor_chunks_franchisor_id'), 'franchisor_chunks', ['franchisor_id'], unique=False)
        op.create_index(op.f('ix_franchisor_chunks_chunk_index'), 'franchisor_chunks', ['chunk_index'], unique=False)
        
        # Create vector index for similarity search (if pgvector extension is available)
        try:
            op.execute("""
                CREATE INDEX IF NOT EXISTS idx_franchisor_chunks_embedding
                ON franchisor_chunks USING ivfflat (embedding vector_cosine_ops)
                WITH (lists = 100)
            """)
        except Exception as e:
            print(f"Warning: Could not create vector index: {e}")
            # Continue anyway - the index is for performance, not functionality


def downgrade() -> None:
    """Drop franchisor_chunks table"""
    
    # Check if table exists before dropping
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    existing_tables = inspector.get_table_names()
    
    if 'franchisor_chunks' in existing_tables:
        # Drop vector index first
        try:
            op.drop_index('idx_franchisor_chunks_embedding', table_name='franchisor_chunks')
        except:
            pass
        
        # Drop regular indexes
        try:
            op.drop_index(op.f('ix_franchisor_chunks_chunk_index'), table_name='franchisor_chunks')
        except:
            pass
        
        try:
            op.drop_index(op.f('ix_franchisor_chunks_franchisor_id'), table_name='franchisor_chunks')
        except:
            pass
        
        try:
            op.drop_index(op.f('ix_franchisor_chunks_id'), table_name='franchisor_chunks')
        except:
            pass
        
        # Drop foreign key constraint
        try:
            op.drop_constraint('fk_franchisor_chunks_franchisor', 'franchisor_chunks', type_='foreignkey')
        except:
            pass
        
        # Drop table
        op.drop_table('franchisor_chunks')
