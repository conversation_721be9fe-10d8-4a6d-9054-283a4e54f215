"""add_lead_reference_tables_simple

Revision ID: 007_lead_refs_simple
Revises: 006_lead_refs
Create Date: 2025-07-23 19:45:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '007_lead_refs_simple'
down_revision = '006_lead_refs'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add lead reference tables if they don't exist"""
    
    # Check if tables exist before creating them
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    existing_tables = inspector.get_table_names()
    
    # Create lead_sources table if it doesn't exist
    if 'lead_sources' not in existing_tables:
        op.create_table(
            'lead_sources',
            sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()'), nullable=False),
            sa.Column('name', sa.String(100), nullable=False, unique=True),
            sa.Column('is_active', sa.<PERSON>(), nullable=False, default=True),
            sa.Column('is_deleted', sa.<PERSON>(), nullable=False, default=False),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
        )
        
        # Create indexes for lead_sources
        op.create_index(op.f('ix_lead_sources_id'), 'lead_sources', ['id'], unique=False)
        op.create_index(op.f('ix_lead_sources_name'), 'lead_sources', ['name'], unique=False)
        op.create_index(op.f('ix_lead_sources_is_active'), 'lead_sources', ['is_active'], unique=False)
        op.create_index(op.f('ix_lead_sources_is_deleted'), 'lead_sources', ['is_deleted'], unique=False)
        
        # Insert default lead sources
        op.execute("""
            INSERT INTO lead_sources (name, is_active, is_deleted) VALUES
            ('Website', true, false),
            ('Phone Call', true, false),
            ('Email', true, false),
            ('Referral', true, false),
            ('Social Media', true, false),
            ('Trade Show', true, false),
            ('Advertisement', true, false),
            ('Other', true, false);
        """)
    
    # Create lead_statuses table if it doesn't exist
    if 'lead_statuses' not in existing_tables:
        op.create_table(
            'lead_statuses',
            sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()'), nullable=False),
            sa.Column('name', sa.String(100), nullable=False, unique=True),
            sa.Column('colour', sa.String(7), nullable=False),  # Hex color code
            sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
            sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
            sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), onupdate=sa.func.now(), nullable=False),
        )
        
        # Create indexes for lead_statuses
        op.create_index(op.f('ix_lead_statuses_id'), 'lead_statuses', ['id'], unique=False)
        op.create_index(op.f('ix_lead_statuses_name'), 'lead_statuses', ['name'], unique=False)
        op.create_index(op.f('ix_lead_statuses_is_active'), 'lead_statuses', ['is_active'], unique=False)
        op.create_index(op.f('ix_lead_statuses_is_deleted'), 'lead_statuses', ['is_deleted'], unique=False)
        
        # Insert default lead statuses
        op.execute("""
            INSERT INTO lead_statuses (name, colour, is_active, is_deleted) VALUES
            ('New', '#007bff', true, false),
            ('Contacted', '#ffc107', true, false),
            ('Qualified', '#28a745', true, false),
            ('Unqualified', '#dc3545', true, false),
            ('Converted', '#17a2b8', true, false),
            ('Lost', '#6c757d', true, false);
        """)
    
    # Update leads table to add foreign key columns if they don't exist
    leads_columns = [col['name'] for col in inspector.get_columns('leads')]
    
    if 'lead_source_id' not in leads_columns:
        op.add_column('leads', sa.Column('lead_source_id', postgresql.UUID(as_uuid=True), nullable=True))
        op.create_index(op.f('ix_leads_lead_source_id'), 'leads', ['lead_source_id'], unique=False)
        op.create_foreign_key('fk_leads_lead_source_id', 'leads', 'lead_sources', ['lead_source_id'], ['id'])
    
    if 'lead_status_id' not in leads_columns:
        op.add_column('leads', sa.Column('lead_status_id', postgresql.UUID(as_uuid=True), nullable=True))
        op.create_index(op.f('ix_leads_lead_status_id'), 'leads', ['lead_status_id'], unique=False)
        op.create_foreign_key('fk_leads_lead_status_id', 'leads', 'lead_statuses', ['lead_status_id'], ['id'])


def downgrade() -> None:
    """Remove lead reference tables"""
    
    # Check if constraints exist before dropping them
    connection = op.get_bind()
    inspector = sa.inspect(connection)
    
    try:
        op.drop_constraint('fk_leads_lead_status_id', 'leads', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_constraint('fk_leads_lead_source_id', 'leads', type_='foreignkey')
    except:
        pass
    
    # Remove columns from leads table if they exist
    leads_columns = [col['name'] for col in inspector.get_columns('leads')]
    
    if 'lead_status_id' in leads_columns:
        op.drop_index(op.f('ix_leads_lead_status_id'), table_name='leads')
        op.drop_column('leads', 'lead_status_id')
    
    if 'lead_source_id' in leads_columns:
        op.drop_index(op.f('ix_leads_lead_source_id'), table_name='leads')
        op.drop_column('leads', 'lead_source_id')
    
    # Drop tables if they exist
    existing_tables = inspector.get_table_names()
    
    if 'lead_statuses' in existing_tables:
        op.drop_table('lead_statuses')
    
    if 'lead_sources' in existing_tables:
        op.drop_table('lead_sources')
