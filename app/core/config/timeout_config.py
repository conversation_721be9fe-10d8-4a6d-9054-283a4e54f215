"""
Timeout Configuration for Document Q&A System

This module centralizes all timeout settings for the document Q&A system
to ensure consistent timeout handling across all components.
"""

from typing import Dict, Any
from pydantic import BaseModel, Field


class TimeoutConfig(BaseModel):
    """Centralized timeout configuration for Q&A operations"""
    
    # Document processing timeouts
    document_ingestion_timeout: int = Field(default=30, ge=5, le=300, description="Timeout for document ingestion in seconds")
    content_extraction_timeout: int = Field(default=60, ge=10, le=600, description="Timeout for content extraction in seconds")
    
    # Q&A operation timeouts
    embedding_generation_timeout: int = Field(default=30, ge=5, le=120, description="Timeout for embedding generation in seconds")
    vector_search_timeout: int = Field(default=45, ge=10, le=180, description="Timeout for vector search operations in seconds")
    answer_generation_timeout: int = Field(default=60, ge=15, le=300, description="Timeout for answer generation in seconds")
    qa_total_timeout: int = Field(default=120, ge=30, le=600, description="Total timeout for complete Q&A operation in seconds")
    
    # Agent operation timeouts
    agent_execution_timeout: int = Field(default=30, ge=5, le=120, description="Timeout for agent execution in seconds")
    conversation_timeout: int = Field(default=45, ge=10, le=180, description="Timeout for conversation operations in seconds")
    
    # API and external service timeouts
    openai_api_timeout: int = Field(default=60, ge=10, le=300, description="Timeout for OpenAI API calls in seconds")
    database_query_timeout: int = Field(default=30, ge=5, le=120, description="Timeout for database queries in seconds")
    external_api_timeout: int = Field(default=30, ge=5, le=120, description="Timeout for external API calls in seconds")
    
    # Background task timeouts
    background_task_timeout: int = Field(default=300, ge=60, le=1800, description="Timeout for background tasks in seconds")
    celery_task_timeout: int = Field(default=600, ge=120, le=3600, description="Timeout for Celery tasks in seconds")
    
    class Config:
        use_enum_values = True


class TimeoutMessages:
    """Standardized timeout messages for consistent user experience"""
    
    # Document processing messages
    DOCUMENT_INGESTION_TIMEOUT = "Document processing is taking longer than expected. Please try again in a few minutes."
    CONTENT_EXTRACTION_TIMEOUT = "Content extraction is taking too long. Please try with a smaller document."
    
    # Q&A operation messages
    EMBEDDING_GENERATION_TIMEOUT = "I'm sorry, the system is taking too long to process your question. Please try again."
    VECTOR_SEARCH_TIMEOUT = "I'm sorry, the search is taking too long. Please try rephrasing your question."
    ANSWER_GENERATION_TIMEOUT = "I'm sorry, generating the answer is taking too long. Please try a simpler question."
    QA_TOTAL_TIMEOUT = "I'm sorry, the system is taking too long to process your question. Please try rephrasing or ask a simpler question."
    
    # Agent operation messages
    AGENT_EXECUTION_TIMEOUT = "The agent is taking too long to respond. Please try again."
    CONVERSATION_TIMEOUT = "I'm sorry, I'm having trouble processing your request. Please try again."
    
    # API timeout messages
    OPENAI_API_TIMEOUT = "The AI service is taking too long to respond. Please try again."
    DATABASE_TIMEOUT = "Database operation is taking too long. Please try again."
    EXTERNAL_API_TIMEOUT = "External service is taking too long to respond. Please try again."
    
    # Background task messages
    BACKGROUND_TASK_TIMEOUT = "Background processing is taking longer than expected. Please check back later."
    CELERY_TASK_TIMEOUT = "Task processing is taking too long. Please try again later."


class TimeoutHandler:
    """Utility class for handling timeouts consistently"""
    
    def __init__(self, config: TimeoutConfig):
        self.config = config
        self.messages = TimeoutMessages()
    
    def get_timeout_message(self, timeout_type: str) -> str:
        """Get standardized timeout message for given timeout type"""
        message_map = {
            'document_ingestion': self.messages.DOCUMENT_INGESTION_TIMEOUT,
            'content_extraction': self.messages.CONTENT_EXTRACTION_TIMEOUT,
            'embedding_generation': self.messages.EMBEDDING_GENERATION_TIMEOUT,
            'vector_search': self.messages.VECTOR_SEARCH_TIMEOUT,
            'answer_generation': self.messages.ANSWER_GENERATION_TIMEOUT,
            'qa_total': self.messages.QA_TOTAL_TIMEOUT,
            'agent_execution': self.messages.AGENT_EXECUTION_TIMEOUT,
            'conversation': self.messages.CONVERSATION_TIMEOUT,
            'openai_api': self.messages.OPENAI_API_TIMEOUT,
            'database': self.messages.DATABASE_TIMEOUT,
            'external_api': self.messages.EXTERNAL_API_TIMEOUT,
            'background_task': self.messages.BACKGROUND_TASK_TIMEOUT,
            'celery_task': self.messages.CELERY_TASK_TIMEOUT,
        }
        return message_map.get(timeout_type, "Operation timed out. Please try again.")
    
    def get_timeout_seconds(self, timeout_type: str) -> int:
        """Get timeout duration for given timeout type"""
        timeout_map = {
            'document_ingestion': self.config.document_ingestion_timeout,
            'content_extraction': self.config.content_extraction_timeout,
            'embedding_generation': self.config.embedding_generation_timeout,
            'vector_search': self.config.vector_search_timeout,
            'answer_generation': self.config.answer_generation_timeout,
            'qa_total': self.config.qa_total_timeout,
            'agent_execution': self.config.agent_execution_timeout,
            'conversation': self.config.conversation_timeout,
            'openai_api': self.config.openai_api_timeout,
            'database': self.config.database_query_timeout,
            'external_api': self.config.external_api_timeout,
            'background_task': self.config.background_task_timeout,
            'celery_task': self.config.celery_task_timeout,
        }
        return timeout_map.get(timeout_type, 30)


# Global timeout configuration instance
timeout_config = TimeoutConfig()
timeout_handler = TimeoutHandler(timeout_config)


def get_timeout_config() -> TimeoutConfig:
    """Get the global timeout configuration"""
    return timeout_config


def get_timeout_handler() -> TimeoutHandler:
    """Get the global timeout handler"""
    return timeout_handler 